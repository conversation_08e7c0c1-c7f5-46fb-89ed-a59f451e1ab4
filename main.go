// main.go - Koopa's Personal Development Assistant
package main

import (
	"log"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/theme"
	"fyne.io/fyne/v2/widget"
)

// AppConfig 儲存應用程式的全域配置
type AppConfig struct {
	K8sConfig      *K8sConfig
	DBConfig       *DatabaseConfig
	AIConfig       *AIConfig
	TaskRunnerConf *TaskRunnerConfig
}

// Application 是主要的應用程式結構
type Application struct {
	app    fyne.App
	window fyne.Window
	config *AppConfig

	// 核心模組
	k8sManager    *K8sManager
	dbManager     *DatabaseManager
	mcpVisualizer *MCPVisualizer
	taskRunner    *TaskRunner
	aiAssistant   *AIAssistant

	// UI 元件
	mainContent *fyne.Container
	sidebar     *widget.List
	statusBar   *StatusBar
}

// NewApplication 建立新的應用程式實例
func NewApplication() *Application {
	// 建立應用程式並套用自定義主題
	myApp := app.NewWithID("io.koopa.devassistant")
	myApp.Settings().SetTheme(&KoopaMD3Theme{})

	// 建立主視窗
	window := myApp.NewWindow("Koopa's Development Assistant")
	window.Resize(fyne.NewSize(1400, 900))

	app := &Application{
		app:    myApp,
		window: window,
		config: loadConfig(),
	}

	// 初始化各個模組
	app.initializeModules()

	// 建立UI
	app.buildUI()

	return app
}

// initializeModules 初始化所有功能模組
func (a *Application) initializeModules() {
	// K8s 管理器
	a.k8sManager = NewK8sManager(a.config.K8sConfig)

	// 資料庫管理器
	a.dbManager = NewDatabaseManager(a.config.DBConfig)

	// MCP 視覺化工具
	a.mcpVisualizer = NewMCPVisualizer()

	// 任務執行器（整合 Makefile/Taskfile）
	a.taskRunner = NewTaskRunner(a.config.TaskRunnerConf)

	// AI 助手（預留介面，後續整合）
	a.aiAssistant = NewAIAssistant(a.config.AIConfig)
}

// buildUI 建立使用者介面
func (a *Application) buildUI() {
	// 建立側邊欄
	a.sidebar = a.createSidebar()

	// 建立主要內容區域
	a.mainContent = container.NewStack()

	// 建立狀態列
	a.statusBar = NewStatusBar()

	// 建立頂部工具列
	toolbar := a.createToolbar()

	// 組合主要佈局
	content := container.NewBorder(
		toolbar,       // 頂部
		a.statusBar,   // 底部
		a.sidebar,     // 左側
		nil,           // 右側
		a.mainContent, // 中央
	)

	a.window.SetContent(content)

	// 設定初始視圖
	a.showDashboard()
}

// createSidebar 建立側邊導航欄
func (a *Application) createSidebar() *widget.List {
	modules := []struct {
		Name   string
		Icon   fyne.Resource
		Action func()
	}{
		{"Dashboard", theme.HomeIcon(), a.showDashboard},
		{"Kubernetes", theme.ComputerIcon(), a.showK8sManager},
		{"Database", theme.StorageIcon(), a.showDBManager},
		{"Task Runner", theme.SettingsIcon(), a.showTaskRunner},
		{"MCP Visualizer", theme.InfoIcon(), a.showMCPVisualizer},
		{"AI Assistant", theme.HelpIcon(), a.showAIAssistant},
	}

	list := widget.NewList(
		func() int { return len(modules) },
		func() fyne.CanvasObject {
			return container.NewHBox(
				widget.NewIcon(nil),
				widget.NewLabel("Module"),
			)
		},
		func(i widget.ListItemID, o fyne.CanvasObject) {
			c := o.(*fyne.Container)
			icon := c.Objects[0].(*widget.Icon)
			label := c.Objects[1].(*widget.Label)

			icon.SetResource(modules[i].Icon)
			label.SetText(modules[i].Name)
		},
	)

	// 設定點擊事件
	list.OnSelected = func(id widget.ListItemID) {
		modules[id].Action()
		a.statusBar.SetText("切換到: " + modules[id].Name)
	}

	// 設定寬度
	list.Resize(fyne.NewSize(250, 0))

	return list
}

// createToolbar 建立頂部工具列
func (a *Application) createToolbar() *widget.Toolbar {
	return widget.NewToolbar(
		widget.NewToolbarAction(theme.DocumentCreateIcon(), func() {
			log.Println("新建項目")
		}),
		widget.NewToolbarSeparator(),
		widget.NewToolbarAction(theme.ViewRefreshIcon(), func() {
			a.refreshCurrentView()
		}),
		widget.NewToolbarAction(theme.SettingsIcon(), func() {
			a.showSettings()
		}),
		widget.NewToolbarSpacer(),
		widget.NewToolbarAction(theme.HelpIcon(), func() {
			a.showHelp()
		}),
	)
}

// View switching methods
func (a *Application) showDashboard() {
	dashboard := NewDashboard(a)
	a.mainContent.Objects = []fyne.CanvasObject{dashboard.Content()}
	a.mainContent.Refresh()
}

func (a *Application) showK8sManager() {
	a.mainContent.Objects = []fyne.CanvasObject{a.k8sManager.Content()}
	a.mainContent.Refresh()
}

func (a *Application) showDBManager() {
	a.mainContent.Objects = []fyne.CanvasObject{a.dbManager.Content()}
	a.mainContent.Refresh()
}

func (a *Application) showTaskRunner() {
	a.mainContent.Objects = []fyne.CanvasObject{a.taskRunner.Content()}
	a.mainContent.Refresh()
}

func (a *Application) showMCPVisualizer() {
	a.mainContent.Objects = []fyne.CanvasObject{a.mcpVisualizer.Content()}
	a.mainContent.Refresh()
}

func (a *Application) showAIAssistant() {
	a.mainContent.Objects = []fyne.CanvasObject{a.aiAssistant.Content()}
	a.mainContent.Refresh()
}

func (a *Application) refreshCurrentView() {
	a.statusBar.SetText("重新整理中...")
	// TODO: 實作重新整理邏輯
}

func (a *Application) showSettings() {
	// TODO: 實作設定視窗
}

func (a *Application) showHelp() {
	// TODO: 實作說明視窗
}

// Run 執行應用程式
func (a *Application) Run() {
	a.window.ShowAndRun()
}

// loadConfig 載入應用程式配置
func loadConfig() *AppConfig {
	// TODO: 從配置檔載入設定
	return &AppConfig{
		K8sConfig:      &K8sConfig{},
		DBConfig:       &DatabaseConfig{},
		AIConfig:       &AIConfig{},
		TaskRunnerConf: &TaskRunnerConfig{},
	}
}

func main() {
	app := NewApplication()
	app.Run()
}
