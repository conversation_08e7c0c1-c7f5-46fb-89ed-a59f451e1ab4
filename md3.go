// theme.go - Material Design 3 inspired theme for Koopa's Dev Assistant
package main

import (
	"image/color"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/canvas"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/theme"
	"fyne.io/fyne/v2/widget"
)

// KoopaMD3Theme 實現 Material Design 3 風格的主題
type KoopaMD3Theme struct{}

// Color 回傳主題顏色
func (m *KoopaMD3Theme) Color(name fyne.ThemeColorName, variant fyne.ThemeVariant) color.Color {
	// Material Design 3 色彩系統
	// 使用深藍色作為主色調，代表專業和信任
	if variant == theme.VariantLight {
		switch name {
		case theme.ColorNameBackground:
			return color.NRGBA{R: 252, G: 252, B: 253, A: 255} // Surface
		case theme.ColorNameForeground:
			return color.NRGBA{R: 28, G: 27, B: 31, A: 255} // On Surface
		case theme.ColorNameButton:
			return color.NRGBA{R: 103, G: 80, B: 164, A: 255} // Primary
		case theme.ColorNamePrimary:
			return color.NRGBA{R: 103, G: 80, B: 164, A: 255} // Primary
		case theme.ColorNameHover:
			return color.NRGBA{R: 236, G: 230, B: 240, A: 255} // Surface Variant
		case theme.ColorNameFocus:
			return color.NRGBA{R: 208, G: 188, B: 255, A: 255} // Primary Container
		case theme.ColorNameSelection:
			return color.NRGBA{R: 208, G: 188, B: 255, A: 255} // Primary Container
		//case theme.ColorNameBorder:
		//	return color.NRGBA{R: 121, G: 116, B: 126, A: 255} // Outline
		case theme.ColorNameScrollBar:
			return color.NRGBA{R: 121, G: 116, B: 126, A: 100}
		case theme.ColorNameShadow:
			return color.NRGBA{R: 0, G: 0, B: 0, A: 30}
		case theme.ColorNameSuccess:
			return color.NRGBA{R: 72, G: 137, B: 82, A: 255} // Success Green
		case theme.ColorNameWarning:
			return color.NRGBA{R: 242, G: 184, B: 75, A: 255} // Warning Amber
		case theme.ColorNameError:
			return color.NRGBA{R: 186, G: 26, B: 26, A: 255} // Error Red
		}
	} else { // Dark mode
		switch name {
		case theme.ColorNameBackground:
			return color.NRGBA{R: 28, G: 27, B: 31, A: 255} // Surface Dark
		case theme.ColorNameForeground:
			return color.NRGBA{R: 230, G: 225, B: 229, A: 255} // On Surface Dark
		case theme.ColorNameButton:
			return color.NRGBA{R: 208, G: 188, B: 255, A: 255} // Primary Dark
		case theme.ColorNamePrimary:
			return color.NRGBA{R: 208, G: 188, B: 255, A: 255} // Primary Dark
		case theme.ColorNameHover:
			return color.NRGBA{R: 49, G: 48, B: 51, A: 255} // Surface Variant Dark
		case theme.ColorNameFocus:
			return color.NRGBA{R: 79, G: 55, B: 139, A: 255} // Primary Container Dark
		case theme.ColorNameSelection:
			return color.NRGBA{R: 79, G: 55, B: 139, A: 255} // Primary Container Dark
		//case theme.ColorNameBorder:
		//	return color.NRGBA{R: 147, G: 143, B: 153, A: 255} // Outline Dark
		case theme.ColorNameScrollBar:
			return color.NRGBA{R: 147, G: 143, B: 153, A: 100}
		case theme.ColorNameShadow:
			return color.NRGBA{R: 0, G: 0, B: 0, A: 60}
		case theme.ColorNameSuccess:
			return color.NRGBA{R: 129, G: 201, B: 139, A: 255} // Success Green Dark
		case theme.ColorNameWarning:
			return color.NRGBA{R: 255, G: 213, B: 79, A: 255} // Warning Amber Dark
		case theme.ColorNameError:
			return color.NRGBA{R: 242, G: 184, B: 181, A: 255} // Error Red Dark
		}
	}

	return theme.DefaultTheme().Color(name, variant)
}

// Font 回傳字型資源
func (m *KoopaMD3Theme) Font(style fyne.TextStyle) fyne.Resource {
	// 可以在這裡加入自定義字型
	// 建議使用 Roboto 或 Inter 字型以符合 Material Design
	return theme.DefaultTheme().Font(style)
}

// Icon 回傳圖示資源
func (m *KoopaMD3Theme) Icon(name fyne.ThemeIconName) fyne.Resource {
	// 可以在這裡加入自定義圖示
	return theme.DefaultTheme().Icon(name)
}

// Size 回傳主題尺寸
func (m *KoopaMD3Theme) Size(name fyne.ThemeSizeName) float32 {
	// Material Design 3 使用更大的觸控目標和更寬鬆的間距
	switch name {
	case theme.SizeNameCaptionText:
		return 11 // 標題文字
	case theme.SizeNameInlineIcon:
		return 20 // 行內圖示
	case theme.SizeNameInnerPadding:
		return 8 // 內部間距
	case theme.SizeNameLineSpacing:
		return 4 // 行距
	case theme.SizeNamePadding:
		return 12 // 一般間距
	case theme.SizeNameScrollBar:
		return 16 // 滾動條寬度
	case theme.SizeNameScrollBarSmall:
		return 8 // 小型滾動條
	case theme.SizeNameSeparatorThickness:
		return 1 // 分隔線厚度
	case theme.SizeNameText:
		return 14 // 一般文字大小
	case theme.SizeNameHeadingText:
		return 24 // 標題文字
	case theme.SizeNameSubHeadingText:
		return 18 // 副標題文字
	case theme.SizeNameInputBorder:
		return 2 // 輸入框邊框
	case theme.SizeNameInputRadius:
		return 12 // 圓角半徑 (MD3 特色)
	}

	return theme.DefaultTheme().Size(name)
}

// StyledButton 自定義元件樣式
type StyledButton struct {
	widget.Button
	importance string // "primary", "secondary", "tertiary"
}

func NewStyledButton(label string, importance string, tapped func()) *StyledButton {
	btn := &StyledButton{
		importance: importance,
	}
	btn.Text = label
	btn.OnTapped = tapped
	btn.ExtendBaseWidget(btn)
	return btn
}

// StatusBar 狀態列元件
type StatusBar struct {
	widget.BaseWidget
	statusLabel *widget.Label
	content     *fyne.Container
}

func NewStatusBar() *StatusBar {
	s := &StatusBar{
		statusLabel: widget.NewLabel("就緒"),
	}

	// 建立狀態列佈局
	s.content = container.NewBorder(
		nil, nil,
		container.NewHBox(
			widget.NewIcon(theme.InfoIcon()),
			s.statusLabel,
		),
		container.NewHBox(
			widget.NewLabel("Koopa's Assistant v1.0"),
		),
		nil,
	)

	s.ExtendBaseWidget(s)
	return s
}

func (s *StatusBar) SetText(text string) {
	s.statusLabel.SetText(text)
}

func (s *StatusBar) CreateRenderer() fyne.WidgetRenderer {
	return widget.NewSimpleRenderer(s.content)
}

// Card MD3 風格的卡片元件
type Card struct {
	widget.BaseWidget
	Title   string
	Content fyne.CanvasObject
	Actions []fyne.CanvasObject
}

func NewCard(title string, content fyne.CanvasObject, actions ...fyne.CanvasObject) *Card {
	c := &Card{
		Title:   title,
		Content: content,
		Actions: actions,
	}
	c.ExtendBaseWidget(c)
	return c
}

func (c *Card) CreateRenderer() fyne.WidgetRenderer {
	// 建立標題
	titleLabel := widget.NewLabelWithStyle(c.Title, fyne.TextAlignLeading, fyne.TextStyle{Bold: true})

	// 建立動作列
	var actionContainer *fyne.Container
	if len(c.Actions) > 0 {
		actionContainer = container.NewHBox(c.Actions...)
	}

	// 組合卡片內容
	var cardContent *fyne.Container
	if actionContainer != nil {
		cardContent = container.NewVBox(
			titleLabel,
			widget.NewSeparator(),
			c.Content,
			widget.NewSeparator(),
			actionContainer,
		)
	} else {
		cardContent = container.NewVBox(
			titleLabel,
			widget.NewSeparator(),
			c.Content,
		)
	}

	// 加入內邊距
	paddedContent := container.NewPadded(cardContent)

	// 建立帶陰影效果的背景
	background := canvas.NewRectangle(color.White)
	background.StrokeColor = color.NRGBA{R: 0, G: 0, B: 0, A: 30}
	background.StrokeWidth = 1
	background.CornerRadius = 12 // MD3 圓角

	return &cardRenderer{
		card:       c,
		background: background,
		content:    paddedContent,
	}
}

type cardRenderer struct {
	card       *Card
	background *canvas.Rectangle
	content    *fyne.Container
}

func (r *cardRenderer) Layout(size fyne.Size) {
	r.background.Resize(size)
	r.content.Resize(size)
}

func (r *cardRenderer) MinSize() fyne.Size {
	return r.content.MinSize()
}

func (r *cardRenderer) Refresh() {
	r.background.Refresh()
	r.content.Refresh()
}

func (r *cardRenderer) Objects() []fyne.CanvasObject {
	return []fyne.CanvasObject{r.background, r.content}
}

func (r *cardRenderer) Destroy() {}
